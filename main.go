package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/go/office/docx"
	"github.com/go/office/ui"
)

func main() {
	// Command line flags
	var (
		webMode = flag.Bool("web", false, "Start web server mode")
		port    = flag.String("port", "8080", "Port for web server")
		input   = flag.String("input", "", "Input DOCX file for CLI mode")
	)
	flag.Parse()

	if *webMode {
		// Start web server
		server := ui.NewServer(*port)
		log.Fatal(server.Start())
	} else if *input != "" {
		// CLI mode - process single file
		err := docx.New(*input)
		if err != nil {
			fmt.Printf("Error processing file: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("File processed successfully!")
	} else {
		// Default: start web server
		fmt.Println("Office Document Processor")
		fmt.Println("Usage:")
		fmt.Println("  -web          Start web server (default)")
		fmt.Println("  -port=8080    Specify port for web server")
		fmt.Println("  -input=file   Process single file in CLI mode")
		fmt.Println()
		fmt.Println("Starting web server by default...")

		server := ui.NewServer(*port)
		log.Fatal(server.Start())
	}
}
