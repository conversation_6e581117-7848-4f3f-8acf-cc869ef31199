# UI Directory Structure

This directory contains all the user interface code for the Office Document Processor application.

## Directory Structure

```
ui/
├── README.md           # This file
├── server.go          # Web server implementation
├── handlers/          # HTTP request handlers
│   └── handlers.go    # Main handlers for upload, download, etc.
├── templates/         # HTML templates
│   └── index.html     # Main page template
└── static/           # Static assets
    ├── css/          # Stylesheets
    │   └── style.css # Main stylesheet
    └── js/           # JavaScript files
        └── app.js    # Main application JavaScript
```

## Features

### Web Interface
- **File Upload**: Drag-and-drop or click to upload DOCX files
- **Real-time Processing**: Shows loading state during document processing
- **Download Results**: Provides download link for processed files
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: User-friendly error messages

### API Endpoints
- `GET /` - Main page
- `POST /upload` - File upload and processing
- `GET /download/{filename}` - Download processed files
- `GET /health` - Health check endpoint

### Static Assets
- **CSS**: Modern, responsive styling with gradient backgrounds
- **JavaScript**: Interactive file upload with drag-and-drop support
- **Templates**: Clean HTML structure with semantic markup

## Usage

### Starting the Web Server
```bash
# Default mode (starts web server on port 8080)
go run main.go

# Explicit web mode
go run main.go -web

# Custom port
go run main.go -web -port=3000
```

### CLI Mode
```bash
# Process a single file
go run main.go -input=path/to/document.docx
```

## File Processing Flow

1. User uploads a DOCX file through the web interface
2. File is validated and saved to the `uploads/` directory
3. Document is processed using the `docx` package
4. Processed file is made available for download
5. User can download the processed document

## Technical Details

### Dependencies
- Standard Go libraries (net/http, html/template, etc.)
- Custom `docx` package for document processing

### File Storage
- Uploaded files are stored in `uploads/` directory
- Files are named with timestamp prefix to avoid conflicts
- Processed files are available for download via `/download/` endpoint

### Security Considerations
- File type validation (only .docx files allowed)
- File size limits (10MB max)
- Secure file serving with proper headers

## Customization

### Styling
Edit `static/css/style.css` to customize the appearance.

### Templates
Modify `templates/index.html` to change the page structure.

### JavaScript
Update `static/js/app.js` to add new interactive features.

### Handlers
Extend `handlers/handlers.go` to add new API endpoints or modify existing behavior.
