package handlers

import (
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/go/office/docx"
)

// Response represents the JSON response structure
type Response struct {
	Success        bool   `json:"success"`
	Message        string `json:"message,omitempty"`
	OriginalFile   string `json:"originalFile,omitempty"`
	ProcessedFile  string `json:"processedFile,omitempty"`
	ProcessingTime string `json:"processingTime,omitempty"`
	DownloadURL    string `json:"downloadUrl,omitempty"`
}

// HomeHandler serves the main page
func HomeHandler(w http.ResponseWriter, r *http.Request) {
	tmpl, err := template.ParseFiles("ui/templates/index.html")
	if err != nil {
		http.Error(w, "Error loading template", http.StatusInternalServerError)
		return
	}

	err = tmpl.Execute(w, nil)
	if err != nil {
		http.Error(w, "Error executing template", http.StatusInternalServerError)
		return
	}
}

// UploadHandler handles file uploads and processing
func UploadHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Set response headers
	w.Header().Set("Content-Type", "application/json")

	// Parse multipart form
	err := r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		sendErrorResponse(w, "Error parsing form data", http.StatusBadRequest)
		return
	}

	// Get file from form
	file, header, err := r.FormFile("file")
	if err != nil {
		sendErrorResponse(w, "Error retrieving file", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Validate file extension
	if filepath.Ext(header.Filename) != ".docx" {
		sendErrorResponse(w, "Only DOCX files are allowed", http.StatusBadRequest)
		return
	}

	// Create uploads directory if it doesn't exist
	uploadsDir := "uploads"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		sendErrorResponse(w, "Error creating uploads directory", http.StatusInternalServerError)
		return
	}

	// Create unique filename
	timestamp := time.Now().Format("20060102_150405")
	originalFilename := fmt.Sprintf("%s_%s", timestamp, header.Filename)
	inputPath := filepath.Join(uploadsDir, originalFilename)

	// Save uploaded file
	dst, err := os.Create(inputPath)
	if err != nil {
		sendErrorResponse(w, "Error saving file", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	_, err = io.Copy(dst, file)
	if err != nil {
		sendErrorResponse(w, "Error copying file", http.StatusInternalServerError)
		return
	}

	// Process the document
	startTime := time.Now()
	err = docx.New(inputPath)
	processingTime := time.Since(startTime)

	if err != nil {
		sendErrorResponse(w, fmt.Sprintf("Error processing document: %v", err), http.StatusInternalServerError)
		return
	}

	// Generate output filename
	outputFilename := fmt.Sprintf("processed_%s", originalFilename)
	outputPath := filepath.Join(uploadsDir, outputFilename)

	// Move the processed file to the correct location
	if err := os.Rename("docx/output.docx", outputPath); err != nil {
		sendErrorResponse(w, "Error moving processed file", http.StatusInternalServerError)
		return
	}

	// Send success response
	response := Response{
		Success:        true,
		OriginalFile:   header.Filename,
		ProcessedFile:  outputFilename,
		ProcessingTime: processingTime.String(),
		DownloadURL:    fmt.Sprintf("/download/%s", outputFilename),
	}

	json.NewEncoder(w).Encode(response)
}

// DownloadHandler serves processed files for download
func DownloadHandler(w http.ResponseWriter, r *http.Request) {
	// Extract filename from URL path
	filename := r.URL.Path[len("/download/"):]
	if filename == "" {
		http.Error(w, "Filename not specified", http.StatusBadRequest)
		return
	}

	// Construct file path
	filePath := filepath.Join("uploads", filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "File not found", http.StatusNotFound)
		return
	}

	// Set headers for file download
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")

	// Serve the file
	http.ServeFile(w, r, filePath)
}

// sendErrorResponse sends a JSON error response
func sendErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	w.WriteHeader(statusCode)
	response := Response{
		Success: false,
		Message: message,
	}
	json.NewEncoder(w).Encode(response)
}
