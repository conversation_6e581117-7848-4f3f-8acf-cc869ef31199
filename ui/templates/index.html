<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Office Document Processor</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Office Document Processor</h1>
            <p>Upload and process your DOCX files</p>
        </header>

        <main>
            <div class="upload-section">
                <h2>Upload Document</h2>
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="file-input-wrapper">
                        <input type="file" id="fileInput" name="file" accept=".docx" required>
                        <label for="fileInput" class="file-input-label">
                            <span class="file-input-text">Choose DOCX file...</span>
                        </label>
                    </div>
                    <button type="submit" class="upload-btn">Process Document</button>
                </form>
            </div>

            <div class="result-section" id="resultSection" style="display: none;">
                <h2>Processing Result</h2>
                <div class="result-content" id="resultContent">
                    <!-- Results will be displayed here -->
                </div>
                <div class="download-section">
                    <a href="#" id="downloadLink" class="download-btn" style="display: none;">Download Processed File</a>
                </div>
            </div>

            <div class="loading" id="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Processing your document...</p>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 Office Document Processor</p>
        </footer>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>
