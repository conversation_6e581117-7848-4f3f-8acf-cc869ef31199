// DOM elements
const uploadForm = document.getElementById('uploadForm');
const fileInput = document.getElementById('fileInput');
const fileInputText = document.querySelector('.file-input-text');
const uploadBtn = document.querySelector('.upload-btn');
const loading = document.getElementById('loading');
const resultSection = document.getElementById('resultSection');
const resultContent = document.getElementById('resultContent');
const downloadLink = document.getElementById('downloadLink');

// File input change handler
fileInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        fileInputText.textContent = file.name;
        uploadBtn.disabled = false;
    } else {
        fileInputText.textContent = 'Choose DOCX file...';
        uploadBtn.disabled = true;
    }
});

// Form submission handler
uploadForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const file = fileInput.files[0];
    if (!file) {
        alert('Please select a file first.');
        return;
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.docx')) {
        alert('Please select a valid DOCX file.');
        return;
    }

    // Show loading state
    showLoading();
    hideResults();

    try {
        // Create FormData object
        const formData = new FormData();
        formData.append('file', file);

        // Send file to server
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        // Hide loading and show results
        hideLoading();
        showResults(result);

    } catch (error) {
        console.error('Error uploading file:', error);
        hideLoading();
        showError('An error occurred while processing your file. Please try again.');
    }
});

// Show loading state
function showLoading() {
    loading.style.display = 'block';
    uploadBtn.disabled = true;
    uploadBtn.textContent = 'Processing...';
}

// Hide loading state
function hideLoading() {
    loading.style.display = 'none';
    uploadBtn.disabled = false;
    uploadBtn.textContent = 'Process Document';
}

// Show results
function showResults(result) {
    resultSection.style.display = 'block';
    
    if (result.success) {
        resultContent.innerHTML = `
            <div class="success-message">
                <h3>✅ Document processed successfully!</h3>
                <p><strong>Original file:</strong> ${result.originalFile}</p>
                <p><strong>Processed file:</strong> ${result.processedFile}</p>
                <p><strong>Processing time:</strong> ${result.processingTime || 'N/A'}</p>
            </div>
        `;
        
        if (result.downloadUrl) {
            downloadLink.href = result.downloadUrl;
            downloadLink.style.display = 'inline-block';
        }
    } else {
        showError(result.message || 'Processing failed');
    }
}

// Hide results
function hideResults() {
    resultSection.style.display = 'none';
    downloadLink.style.display = 'none';
}

// Show error message
function showError(message) {
    resultSection.style.display = 'block';
    resultContent.innerHTML = `
        <div class="error-message" style="border-left-color: #e53e3e; background: #fed7d7;">
            <h3>❌ Error</h3>
            <p>${message}</p>
        </div>
    `;
}

// Drag and drop functionality
const fileInputLabel = document.querySelector('.file-input-label');

fileInputLabel.addEventListener('dragover', function(e) {
    e.preventDefault();
    this.style.borderColor = '#667eea';
    this.style.background = '#edf2f7';
});

fileInputLabel.addEventListener('dragleave', function(e) {
    e.preventDefault();
    this.style.borderColor = '#cbd5e0';
    this.style.background = '#f7fafc';
});

fileInputLabel.addEventListener('drop', function(e) {
    e.preventDefault();
    this.style.borderColor = '#cbd5e0';
    this.style.background = '#f7fafc';
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
    }
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Reset form state
    uploadForm.reset();
    hideResults();
    hideLoading();
    uploadBtn.disabled = true;
});
