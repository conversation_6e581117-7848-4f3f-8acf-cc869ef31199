package ui

import (
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/go/office/ui/handlers"
)

// Server represents the web server
type Server struct {
	Port string
}

// NewServer creates a new server instance
func NewServer(port string) *Server {
	if port == "" {
		port = "8080"
	}
	return &Server{Port: port}
}

// Start starts the web server
func (s *Server) Start() error {
	// Create uploads directory if it doesn't exist
	if err := os.MkdirAll("uploads", 0755); err != nil {
		return fmt.Errorf("error creating uploads directory: %v", err)
	}

	// Setup routes
	s.setupRoutes()

	// Start server
	addr := ":" + s.Port
	log.Printf("Starting web server on http://localhost%s", addr)
	log.Printf("Open your browser and navigate to: http://localhost%s", addr)
	
	return http.ListenAndServe(addr, nil)
}

// setupRoutes configures all the HTTP routes
func (s *Server) setupRoutes() {
	// Serve static files
	fs := http.FileServer(http.Dir("ui/static/"))
	http.Handle("/static/", http.StripPrefix("/static/", fs))

	// API routes
	http.HandleFunc("/", handlers.HomeHandler)
	http.HandleFunc("/upload", handlers.UploadHandler)
	http.HandleFunc("/download/", handlers.DownloadHandler)

	// Health check endpoint
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "healthy", "service": "office-document-processor"}`))
	})
}
